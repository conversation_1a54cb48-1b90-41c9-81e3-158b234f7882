import { useLayoutEffect } from 'react'
import gsap from 'gsap'

const Testimonials = () => {
  const reviews = [
    {
      name: 'Person 1',
      img: 'https://placehold.co/100x100',
      quote: 'Lorem ipsum dolor sit amet consectetur.',
    },
    {
      name: 'Person 2',
      img: 'https://placehold.co/100x100',
      quote: 'Lorem ipsum dolor sit amet consectetur.',
    },
    {
      name: 'Person 3',
      img: 'https://placehold.co/100x100',
      quote: 'Lorem ipsum dolor sit amet consectetur.',
    },
  ]

  useLayoutEffect(() => {
    gsap.from('.review', { y: 40, opacity: 0, duration: 1, stagger: 0.3 })
  }, [])

  return (
    <section className="px-8 py-16 bg-gray-50">
      <h2 className="text-3xl font-bold text-center mb-10">
        What Our Customers Say About Us
      </h2>
      <div className="grid md:grid-cols-3 gap-8">
        {reviews.map((r) => (
          <div key={r.name} className="review bg-white p-4 rounded shadow text-center">
            <img
              className="w-20 h-20 rounded-full mx-auto mb-4"
              src={r.img}
              alt={r.name}
            />
            <h4 className="font-semibold">{r.name}</h4>
            <p className="text-sm text-gray-600 mt-2">{r.quote}</p>
          </div>
        ))}
      </div>
    </section>
  )
}
export default Testimonials