import { useLayoutEffect, useRef } from 'react';
import gsap from 'gsap';

const HeroSection = () => {
  const root = useRef(null);

  useLayoutEffect(() => {
    const ctx = gsap.context(() => {
      gsap.from('.hero-item', { y: 60, opacity: 0, duration: 1, stagger: 0.2 });
    }, root);

    return () => ctx.revert();
  }, []);

  return (
    <section ref={root} className="grid md:grid-cols-2 items-center gap-8 px-8 py-16 bg-white">
      <div className="hero-item">
        <h1 className="text-4xl font-bold mb-4">
          It's not just a Food It's a Experience!
        </h1>
        <p className="mb-6 text-gray-600">
          This is Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quas, velit!
        </p>
        <div className="flex gap-4">
          <button className="px-6 py-2 bg-rose-500 text-white rounded hover:bg-rose-600">
            Our Menu
          </button>
          <button className="px-6 py-2 border border-gray-400 rounded hover:bg-gray-100">
            About Us
          </button>
        </div>
      </div>
      <div className="hero-item">
        <img
          className="w-full rounded-lg"
          src="https://placehold.co/600x400"
          alt="Hero"
        />
      </div>
    </section>
  );
};
export default HeroSection;