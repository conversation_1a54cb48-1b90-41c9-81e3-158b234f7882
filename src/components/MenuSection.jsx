import ProductCard from './ProductCard';

const MenuSection = () => {
  const items = [
    { title: 'Cakes & Pastries', img: 'https://placehold.co/150', price: 'Rs. 100' },
    { title: 'Sandwich', img: 'https://placehold.co/150', price: 'Rs. 120' },
    { title: 'Burger', img: 'https://placehold.co/150', price: 'Rs. 150' },
    { title: 'Pizza', img: 'https://placehold.co/150', price: 'Rs. 200' },
  ];

  return (
    <section className="px-8 py-16 bg-gray-50">
      <h2 className="text-3xl font-bold text-center mb-10">Eat Healthy</h2>
      <div className="flex justify-center gap-4 mb-12 text-sm">
        {['Cakes & Pastries', 'Sandwich', 'Burger', 'Pizza'].map((cat) => (
          <button key={cat} className="px-4 py-1 rounded-full bg-rose-100 hover:bg-rose-200">
            {cat}
          </button>
        ))}
      </div>
      <div className="grid md:grid-cols-3 gap-8">
        {items.map((item) => (
          <ProductCard key={item.title} {...item} />
        ))}
      </div>
      <div className="flex justify-center mt-8">
        <button className="px-6 py-2 bg-rose-500 text-white rounded hover:bg-rose-600">
          More Details
        </button>
      </div>
    </section>
  );
};
export default MenuSection;