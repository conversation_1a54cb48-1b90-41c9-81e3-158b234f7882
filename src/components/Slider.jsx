import { useLayoutEffect, useRef, useState, useCallback, useEffect } from 'react';

// Data untuk setiap card
const foodData = [
  {
    id: 0,
    image: "/carousel/carousel-pizza_1-small.png",
    title: "Margherita Pizza",
    subtitle: "Classic & Simple",
    description: "Taste the tradition!",
    quote: "The perfect balance of tomato, mozzarella, and fresh basil."
  },
  {
    id: 1,
    image: "/carousel/carousel-pizza_2-small.png",
    title: "Pepperoni Pizza",
    subtitle: "Bold & Spicy",
    description: "Feel the heat!",
    quote: "America's favorite pizza with crispy pepperoni slices."
  },
  {
    id: 2,
    image: "/carousel/carousel-pizza_3-small.png",
    title: "Veggie Supreme",
    subtitle: "Fresh & Healthy",
    description: "Love the greens!",
    quote: "A garden of fresh vegetables on a crispy crust."
  },
  {
    id: 3,
    image: "/carousel/carousel-pizza_4_small.png",
    title: "Meat Lovers",
    subtitle: "Rich & Hearty",
    description: "Savor the protein!",
    quote: "For those who believe more meat means more flavor."
  },
  {
    id: 4,
    image: "/carousel/carousel-pizza_5-small.png",
    title: "Hawaiian Pizza",
    subtitle: "Sweet & Savory",
    description: "Tropical delight!",
    quote: "The controversial combination that divides pizza lovers."
  }
];

export default function FoodCarousel() {
  const totalCards = 5;
  const [currentCenter, setCurrentCenter] = useState(2);
  const [isUserInteracting, setIsUserInteracting] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const isAnimating = useRef(false);
  const cardElements = useRef(new Map());
const baseSize = 20;

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const setCardRef = useCallback((cardId) => (el) => {
    if (el) cardElements.current.set(cardId, el);
    else cardElements.current.delete(cardId);
  }, []);

  const computeCardPosition = useCallback((cardId) => {
    const offset = cardId - currentCenter;
    let normalizedOffset = offset;
    if (normalizedOffset > 2) normalizedOffset -= totalCards;
    if (normalizedOffset < -2) normalizedOffset += totalCards;

    const positionMap = {
      "-2": { x: "-35vw", y: "10vw", scale: 0.7 },
      "-1": { x: "-20vw", y: "0vw", scale: 0.8 },
      "0":  { x: "0vw",   y: "-5vw", scale: 1.0 },
      "1":  { x: "20vw",  y: "0vw", scale: 0.8 },
      "2":  { x: "35vw",  y: "10vw", scale: 0.7 }
    };

    const { x, y, scale } = positionMap[normalizedOffset.toString()] || { x: "0vw", y: "10vw", scale: 0 };


    const size = baseSize * scale;
    const opacity = scale === 0 ? 0 : 1;

    return {
      x,
      y,
      z: 0,
      scale,
      opacity,
      zIndex: Math.round(scale * 10),
      size
    };
  }, [currentCenter, totalCards]);

  const applyCardTransformations = useCallback((duration = 0.6) => {
    if (isAnimating.current) return;
    isAnimating.current = true;

    for (const [cardId, cardEl] of cardElements.current.entries()) {
      if (!cardEl) continue;
      const { x, y, z, scale, opacity, zIndex, size } = computeCardPosition(cardId);
      cardEl.style.transform = `translateX(-50%) translate3d(${x}, ${y}, ${z}px) scale(${scale})`;
      cardEl.style.opacity = opacity;
      cardEl.style.zIndex = zIndex;
      cardEl.style.width = `${size}vw`;
      cardEl.style.height = `${size}vw`;
    }

    setTimeout(() => {
      isAnimating.current = false;
    }, duration * 1000);
  }, [computeCardPosition]);

  const navigate = useCallback((direction) => {
    if (isAnimating.current) return;
    setIsUserInteracting(true);
    setCurrentCenter(prev => {
      let newCenter = direction === 'next' ? prev + 1 : prev - 1;
      if (newCenter >= totalCards) newCenter = 0;
      if (newCenter < 0) newCenter = totalCards - 1;
      return newCenter;
    });
    setTimeout(() => setIsUserInteracting(false), 2000);
  }, []);

  const next = useCallback(() => navigate('next'), [navigate]);
  const prev = useCallback(() => navigate('prev'), [navigate]);

  useEffect(() => {
    applyCardTransformations();
  }, [currentCenter, applyCardTransformations]);

  useLayoutEffect(() => {
    applyCardTransformations(0);
  }, [applyCardTransformations]);

  return (
    <div
      className="relative w-full min-h-screen flex flex-col items-center justify-center py-8 md:py-16"
      style={{
        backgroundImage: 'url(/carousel/bg2-carousel.webp)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      <div className="relative w-full px-4" style={{ perspective: '1200px' }}>
        <div className={`relative z-10 ${isMobile ? 'h-[250px]' : 'h-[380px]'} mb-6 md:mb-8 flex items-center justify-center`}>
          {[...Array(totalCards).keys()].map((cardId) => {
            const position = computeCardPosition(cardId);
            return (
              <div
                key={cardId}
                ref={setCardRef(cardId)}
                className="card-item absolute left-1/2 top-0 bg-gray-200 rounded-full shadow-md cursor-pointer"
                style={{
                  width: `${position.size}px`,
                  height: `${position.size}px`,
                  transform: `translateX(-50%) translate3d(${position.x}, ${position.y}, ${position.z}px) scale(${position.scale})`,
                  willChange: 'transform, opacity',
                  transformStyle: 'preserve-3d',
                  transition: 'transform 0.6s ease, opacity 0.6s ease',
                  opacity: position.opacity,
                  zIndex: position.zIndex,
                }}
                onClick={() => {
                  if (cardId !== currentCenter && !isAnimating.current) {
                    setIsUserInteracting(true);
                    setCurrentCenter(cardId);
                    setTimeout(() => setIsUserInteracting(false), 2000);
                  }
                }}
              >
                <div className="w-full h-full flex items-center justify-center">
                  <img
                    src={foodData[cardId].image}
                    alt={foodData[cardId].title}
                    className="w-full h-full object-cover rounded-full"
                  />
                </div>
              </div>
            );
          })}
        </div>

        {/* Panel Overlay */}
        <div  style={{ top: `calc(50px + 1vw + ${baseSize}vw)` }} className="absolute left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[100] w-[20vw] h-[15vw] bg-gray-200 rounded-xl shadow-md text-center flex flex-col justify-center py-4 px-6">
          <h2 className="text-xl md:text-2xl font-bold text-gray-800">{foodData[currentCenter].title}</h2>
          <h3 className="text-lg md:text-xl font-semibold text-gray-700">{foodData[currentCenter].subtitle}</h3>
          <h4 className="text-lg md:text-xl font-semibold text-gray-700 mb-2">{foodData[currentCenter].description}</h4>
          <blockquote className="text-gray-600 italic text-sm md:text-base leading-tight">"{foodData[currentCenter].quote}"</blockquote>
       

        {/* Kontrol Navigasi */}
        <div
        style={{ width: "calc( 20vw + 120px )" }}
          className={`z-[100] left-[-60px] absolute flex justify-between items-center max-w-2xl mx-auto ${isMobile ? 'gap-x-2' : 'gap-x-4'}`}
        >
          <button onClick={prev} className="relative z-[100] flex-shrink-0 w-10 h-10 md:w-12 md:h-12 bg-gray-200 rounded-full shadow-md flex items-center justify-center text-gray-600 transition-transform active:scale-90 hover:scale-105">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" /></svg>
          </button>

          <button onClick={next} className="relative z-[100] flex-shrink-0 w-10 h-10 md:w-12 md:h-12 bg-gray-200 rounded-full shadow-md flex items-center justify-center text-gray-600 transition-transform active:scale-90 hover:scale-105">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" /></svg>
          </button>
        </div>
         </div>
      </div>
    </div>
  );
}
